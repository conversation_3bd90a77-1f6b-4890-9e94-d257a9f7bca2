# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

import os
# 设置使用 Hugging Face 国内镜像
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'  # 或者使用其他可用的国内镜像

from vllm import LLM, SamplingParams

Post = "#CWSJ头条 美中同意大幅降低关税：美国将对华加征的关税削减至30%，中国则将对美关税下调至10%，同时双方计划展开进一步的贸易谈判。"
CommentonPost = "可以的，一赢各表� 目前美对中：四月前+10%（强调三月的20%芬太尼关税保留，也就是30%） 中对美：四月前+10%（三月对芬太尼关税的反制没说取消，默默收钱） 川普可以给maga交代：30%对10%，咱们赢了。老中也可以说：10%对10%，咱们赢了。大家一起赚钱，一起有面子"
CommentonComment= "老中实诚人，不玩这种文字游戏。"

# Sample prompts.
baselinePrompt = [
    f"Your task is to determine the intention of the CommentonComment by assigning one of the following labels: Sharing Infomation(提供事实或知识传递客观信息), Assertion Persuation(表达观点或明确立场，展示说话者的判断), Affiliating and Supporting(表达对他人观点或行为的积极态度,如认同、支持、感谢), Dissenting and Conflicting(表达对他人观点或行为的否定、批评、讽刺), Expressing Feelings(直接通过语言表露说话者的情感状态), Requesting Infomation(通过提问或请求获取信息或他人观点), Calling for Action(指令或倡议促使他人采取行动) or Unclear Intent.Post: {Post} CommentonPost: {CommentonPost} CommentonComment: {CommentonComment}.Please think step by step and give your answer in the format of Label: Explanation."
]
# Create a sampling params object.
sampling_params = SamplingParams(temperature=0, top_p=0.95,max_tokens=1024)


def main():
    # Create an LLM.
    llm = LLM(model="Qwen/Qwen2.5-7B-Instruct")
    # Generate texts from the prompts.
    # The output is a list of RequestOutput objects
    # that contain the prompt, generated text, and other information.
    outputs = llm.generate(baselinePrompt, sampling_params)
    # Print the outputs.
    print("\nGenerated Outputs:\n" + "-" * 60)
    for output in outputs:
        prompt = output.prompt
        generated_text = output.outputs[0].text
        print(f"Prompt:    {prompt!r}")
        print(f"Output:    {generated_text!r}")
        print("-" * 60)


if __name__ == "__main__":
    main()