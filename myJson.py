import os
import json
import warnings

warnings.filterwarnings('ignore')

# 设置环境变量
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

from vllm import LLM, SamplingParams
from vllm.sampling_params import GuidedDecodingParams

# 初始化LLM
llm = LLM(model="Qwen/Qwen2.5-7B-Instruct")

# 第一步：内容规范化的JSON schema
normalization_schema = {
    "type": "object",
    "properties": {
        "Post": {
            "type": "array",
            "items": {"type": "string"},
            "description": "The revised Post content"
        },
        "CommentonPost": {
            "type": "array",
            "items": {"type": "string"},
            "description": "The revised CommentonPost content"
        },
        "CommentonComment": {
            "type": "array",
            "items": {"type": "string"},
            "description": "The revised CommentonComment content"
        }
    },
    "required": ["Post", "CommentonPost", "CommentonComment"],
    "additionalProperties": False
}

# 第二步：主客观分析的JSON schema
analysis_schema = {
    "type": "object",
    "properties": {
        "objective": {
            "type": "object",
            "properties": {
                "Post": {"type": "array", "items": {"type": "string"}},
                "CommentonPost": {"type": "array", "items": {"type": "string"}},
                "CommentonComment": {"type": "array", "items": {"type": "string"}}
            },
            "required": ["Post", "CommentonPost", "CommentonComment"]
        },
        "subjective": {
            "type": "object",
            "properties": {
                "Post": {"type": "array", "items": {"type": "string"}},
                "CommentonPost": {"type": "array", "items": {"type": "string"}},
                "CommentonComment": {"type": "array", "items": {"type": "string"}}
            },
            "required": ["Post", "CommentonPost", "CommentonComment"]
        }
    },
    "required": ["objective", "subjective"],
    "additionalProperties": False
}

# 第三步：情感表达分析的JSON schema
emotion_schema = {
    "type": "object",
    "properties": {
        "ExpressingFeelings": {
            "type": "boolean",
            "description": "Whether the content expresses emotional feelings"
        },
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of how subjective and objective parts support the judgment"
        }
    },
    "required": ["ExpressingFeelings", "Analysis"],
    "additionalProperties": False
}

# 信息分享意图分析的JSON schema
sharing_info_schema = {
    "type": "object",
    "properties": {
        "SharingInformation": {
            "type": "boolean",
            "description": "Whether the content shares factual information or knowledge"
        },
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of how subjective and objective parts support the judgment"
        }
    },
    "required": ["SharingInformation", "Analysis"],
    "additionalProperties": False
}

# 表达主张意图分析的JSON schema
assertion_schema = {
    "type": "object",
    "properties": {
        "AssertionPersuasion": {
            "type": "boolean",
            "description": "Whether the content expresses viewpoints or clear stance"
        },
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of how subjective and objective parts support the judgment"
        }
    },
    "required": ["AssertionPersuasion", "Analysis"],
    "additionalProperties": False
}

# 认同与联结意图分析的JSON schema
affiliating_schema = {
    "type": "object",
    "properties": {
        "AffiliatingSupporting": {
            "type": "boolean",
            "description": "Whether the content expresses positive attitude towards others' opinions or behaviors"
        },
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of how subjective and objective parts support the judgment"
        }
    },
    "required": ["AffiliatingSupporting", "Analysis"],
    "additionalProperties": False
}

# 分歧与冲突意图分析的JSON schema
dissenting_schema = {
    "type": "object",
    "properties": {
        "DissentingConflicting": {
            "type": "boolean",
            "description": "Whether the content expresses disagreement, criticism, or sarcasm"
        },
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of how subjective and objective parts support the judgment"
        }
    },
    "required": ["DissentingConflicting", "Analysis"],
    "additionalProperties": False
}

# 寻求信息意图分析的JSON schema
requesting_info_schema = {
    "type": "object",
    "properties": {
        "RequestingInformation": {
            "type": "boolean",
            "description": "Whether the content seeks information or others' opinions through questions or requests"
        },
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of how subjective and objective parts support the judgment"
        }
    },
    "required": ["RequestingInformation", "Analysis"],
    "additionalProperties": False
}

# 号召行动意图分析的JSON schema
calling_action_schema = {
    "type": "object",
    "properties": {
        "CallingForAction": {
            "type": "boolean",
            "description": "Whether the content calls for or advocates others to take action"
        },
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of how subjective and objective parts support the judgment"
        }
    },
    "required": ["CallingForAction", "Analysis"],
    "additionalProperties": False
}

# 意图不明分析的JSON schema
unclear_intent_schema = {
    "type": "object",
    "properties": {
        "UnclearIntent": {
            "type": "boolean",
            "description": "Whether the content has unclear or ambiguous intent"
        },
        "Analysis": {
            "type": "string",
            "description": "Detailed analysis of how subjective and objective parts support the judgment"
        }
    },
    "required": ["UnclearIntent", "Analysis"],
    "additionalProperties": False
}

# 定义要注入的内容变量
post_content = "#CWSJ头条 美中同意大幅降低关税：美国将对华加征的关税削减至30%，中国则将对美关税下调至10%，同时双方计划展开进一步的贸易谈判。"
comment_on_post = "关键是加收的那部分关税基本就是中国对美能买的所有物品了，农产品和能源。剩下的芯片禁售，加不加无所谓，而且中国也确实需要芯片。就跟稀土一样，中国如果不管制稀土美国一样会给稀土豁免关税。"
comment_on_comment = "没用，高端芯片和稀土中美都不会放开"

# 第一步：内容规范化
normalization_params = SamplingParams(
    guided_decoding=GuidedDecodingParams(json=normalization_schema),
    temperature=0,
    top_p=1,
    max_tokens=1024
)

normalization_prompt = f"""Please analyze and complete the missing grammatical parts such as subject, verb, object of the following content, and output in the specified JSON format:
- Post: {post_content}
- CommentonPost: {comment_on_post}
- CommentonComment: {comment_on_comment}

Return the result in this exact JSON format:
{{
    "Post": ["revised post content"],
    "CommentonPost": ["revised comment on post"],
    "CommentonComment": ["revised comment on comment"]
}}"""

print("=== The 1st step：Normalization of the Content ===")
outputs = llm.generate(
    prompts=normalization_prompt,
    sampling_params=normalization_params,
)

normalized_text = outputs[0].outputs[0].text
print("Normalized JSON:")
print(normalized_text)

try:
    normalized_data = json.loads(normalized_text)
    print("\nParsed normalized JSON:")
    print(json.dumps(normalized_data, indent=2, ensure_ascii=False))
    
    # 第二步：主客观分析
    analysis_params = SamplingParams(
        guided_decoding=GuidedDecodingParams(json=analysis_schema),
        temperature=0,
        top_p=1,
        max_tokens=1024
    )
    
    analysis_prompt = f"""请分析以下内容，将其分为客观事实和主观观点两部分：

输入内容：
{json.dumps(normalized_data, ensure_ascii=False, indent=2)}

Please divide the content of Post, CommentonPost and CommentonComment into subjective and objective parts.

输出格式：
{{
  "objective": {{
    "Post": ["客观事实1", "客观事实2"],
    "CommentonPost": ["客观事实1", "客观事实2"],
    "CommentonComment": ["客观事实1"]
  }},
  "subjective": {{
    "Post": ["主观观点1", "主观观点2"],
    "CommentonPost": ["主观观点1", "主观观点2"],
    "CommentonComment": ["主观观点1"]
  }}
}}"""

    print("\n=== 第二步：主客观分析 ===")
    analysis_outputs = llm.generate(
        prompts=analysis_prompt,
        sampling_params=analysis_params,
    )
    
    analysis_text = analysis_outputs[0].outputs[0].text
    print("Analysis JSON:")
    print(analysis_text)
    
    try:
        analysis_data = json.loads(analysis_text)
        print("\nFinal Analysis Result:")
        print(json.dumps(analysis_data, indent=2, ensure_ascii=False))

        # 第三步：情感表达分析  emotion_schema
        emotion_params = SamplingParams(
            guided_decoding=GuidedDecodingParams(json=emotion_schema),
            temperature=0,
            top_p=1,
            max_tokens=1024
        )

        emotion_prompt = f"""基于以下主客观分离后的内容，分析CommentonComment是否符合"情感表达"(Expressing Feelings)意图。
        情感表达定义：直接通过语言表露说话者的情感状态。
        输入内容：
        {json.dumps(analysis_data, ensure_ascii=False, indent=2)}

       请分析CommentonComment中的主观部分是如何体现或没有体现情感表达意图的，以及客观部分如何支撑这一判断。

        输出格式：
        {{
            "ExpressingFeelings": true/false,
            "Analysis": "详细分析CommentonComment中主观部分如何体现/没有体现情感表达，以及客观语料如何支撑这一分析的一段话"
        }}
        """

        print("\n=== 第三步：情感表达分析 ===")
        emotion_outputs = llm.generate(
            prompts=emotion_prompt,
            sampling_params=emotion_params,
        )
        
        emotion_text = emotion_outputs[0].outputs[0].text
        print("Emotion Analysis JSON:")
        print(emotion_text)
        
        try:
            emotion_data = json.loads(emotion_text)
            print("\nFinal Emotion Analysis Result:")
            print(json.dumps(emotion_data, indent=2, ensure_ascii=False))
        except json.JSONDecodeError as e:
            print(f"\nEmotion JSON parsing error: {e}")
        
    except json.JSONDecodeError as e:
        print(f"\nAnalysis JSON parsing error: {e}")
        
except json.JSONDecodeError as e:
    print(f"\nNormalization JSON parsing error: {e}")